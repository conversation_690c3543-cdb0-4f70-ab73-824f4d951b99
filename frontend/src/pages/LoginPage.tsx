/**
 * Login Page Component
 * Full page wrapper for the login form with Kenyan theme and branding
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import LoginForm from '../components/LoginForm';
import { useAuth } from '../contexts/AuthContext';
import { getDashboardRoute } from '../utils/roleBasedRouting';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, isLoading } = useAuth();
  const { t } = useTranslation();
  
  const [loginStatus, setLoginStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      const dashboardRoute = getDashboardRoute(user);
      navigate(dashboardRoute, { replace: true });
    }
  }, [isAuthenticated, user, navigate]);

  /**
   * Handle successful login
   */
  const handleLoginSuccess = () => {
    setLoginStatus('success');
    setErrorMessage('');

    // Redirect immediately on success
    if (user) {
      const dashboardRoute = getDashboardRoute(user);
      navigate(dashboardRoute, { replace: true });
    }
  };

  /**
   * Handle login error
   */
  const handleLoginError = (error: string) => {
    setLoginStatus('error');
    setErrorMessage(error);
    console.error('Login failed:', error);
  };

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8" style={{ backgroundColor: '#E2FCF7' }}>
      {/* Header */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <img
              src="/logo.png"
              alt="CivicAI Logo"
              className="h-16 w-auto"
              onError={(e) => {
                // Fallback if logo.png doesn't exist
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <div className="hidden w-16 h-16 rounded-lg items-center justify-center" style={{ backgroundColor: '#0D3C43' }}>
              <span className="text-white font-bold text-2xl">C</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('auth.welcomeBack')}
          </h1>
          <p className="text-gray-600">
            {t('auth.signInToContinue')}
          </p>
        </div>
      </div>

      {/* Error Message */}
      {loginStatus === 'error' && errorMessage && (
        <div className="sm:mx-auto sm:w-full sm:max-w-md mt-6">
          <div className="bg-red-50 border border-red-200 rounded-md p-4 flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" />
            <div>
              <p className="text-red-800 font-medium">{t('common.error')}</p>
              <p className="text-red-700 text-sm">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Login Form */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <LoginForm
          onSuccess={handleLoginSuccess}
          onError={handleLoginError}
        />
      </div>

      {/* Simplified Navigation */}
      <div className="mt-6 text-center">
        <p className="text-sm text-gray-600">
          {t('auth.dontHaveAccount')}{' '}
          <button
            onClick={() => navigate('/register')}
            className="font-medium transition-colors"
            style={{ color: '#0D3C43' }}
          >
            {t('auth.signUp')}
          </button>
        </p>
      </div>


    </div>
  );
};

export default LoginPage;
